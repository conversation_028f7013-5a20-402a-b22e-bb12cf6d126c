<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍍 جزيرة الأناناس - مغامرة اللغة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, Arial, sans-serif;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd23f, #4CAF50);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
            position: relative;
            z-index: 1;
        }

        /* Floating elements animation */
        .floating-element {
            position: absolute;
            animation: float 6s ease-in-out infinite;
            z-index: 0;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        .floating-pineapple {
            font-size: clamp(30px, 5vw, 50px);
            top: 10%;
            right: 10%;
            animation-delay: -2s;
        }

        .floating-palm {
            font-size: clamp(25px, 4vw, 40px);
            top: 20%;
            left: 5%;
            animation-delay: -4s;
        }

        .floating-sun {
            font-size: clamp(35px, 6vw, 60px);
            top: 5%;
            left: 50%;
            animation-delay: -1s;
        }

        /* Game screens */
        .screen {
            display: none;
            min-height: 100vh;
            padding: 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .screen.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .game-title {
            font-size: clamp(2rem, 8vw, 4rem);
            color: #2c5530;
            text-shadow: 3px 3px 0px #ffd23f, -1px -1px 0px #ff6b35;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .story-text {
            background: rgba(255, 255, 255, 0.9);
            padding: clamp(15px, 4vw, 30px);
            border-radius: 20px;
            margin: 20px auto;
            max-width: 90%;
            font-size: clamp(1rem, 3vw, 1.4rem);
            line-height: 1.6;
            color: #2c5530;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
            border: 3px solid #f7931e;
        }

        .btn {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: clamp(12px, 3vw, 20px) clamp(20px, 5vw, 40px);
            font-size: clamp(1rem, 3vw, 1.3rem);
            border-radius: 25px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s;
            box-shadow: 0 6px 15px rgba(0,0,0,0.3);
            border: 3px solid #fff;
            font-weight: bold;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.4);
            background: linear-gradient(45deg, #45a049, #4CAF50);
        }

        .btn:active {
            transform: translateY(0px);
        }

        .btn.wrong {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            animation: shake 0.5s;
            transform: scale(0.95);
        }

        .btn.correct {
            background: linear-gradient(45deg, #4CAF50, #2e7d32);
            animation: pulse 0.5s;
            transform: scale(1.05);
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .progress-bar {
            width: 100%;
            max-width: 400px;
            height: 20px;
            background: rgba(255,255,255,0.3);
            border-radius: 10px;
            margin: 20px auto;
            overflow: hidden;
            border: 2px solid #2c5530;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 8px;
        }

        .keys-collected {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .key {
            font-size: clamp(25px, 5vw, 40px);
            filter: grayscale(100%);
            transition: all 0.3s;
        }

        .key.collected {
            filter: grayscale(0%);
            animation: collectKey 0.5s;
        }

        @keyframes collectKey {
            0% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.5) rotate(180deg); }
            100% { transform: scale(1) rotate(360deg); }
        }

        .challenge-image {
            max-width: 200px;
            max-height: 200px;
            width: clamp(150px, 30vw, 200px);
            height: clamp(150px, 30vw, 200px);
            object-fit: contain;
            border-radius: 20px;
            border: 5px solid #f7931e;
            background: white;
            padding: 10px;
            margin: 20px auto;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .word-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
            max-width: 400px;
            margin: 20px auto;
        }

        .sentence-builder {
            background: rgba(255,255,255,0.9);
            padding: 20px;
            border-radius: 20px;
            margin: 20px auto;
            max-width: 90%;
            border: 3px solid #f7931e;
        }

        .sentence-parts {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }

        .word-part {
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 15px;
            cursor: move;
            font-size: clamp(0.9rem, 2.5vw, 1.1rem);
            border: 2px solid #fff;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }

        .drop-zone {
            min-height: 50px;
            border: 3px dashed #f7931e;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 10px 0;
            background: rgba(255,255,255,0.5);
            font-size: clamp(0.9rem, 2.5vw, 1.1rem);
        }

        .drop-zone.filled {
            border-style: solid;
            background: #4CAF50;
            color: white;
        }

        .reading-passage {
            background: rgba(255,255,255,0.95);
            padding: 20px;
            border-radius: 20px;
            margin: 20px auto;
            max-width: 90%;
            font-size: clamp(1rem, 3vw, 1.2rem);
            line-height: 1.8;
            color: #2c5530;
            border: 3px solid #f7931e;
            text-align: right;
        }

        .question {
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 15px;
            margin: 15px auto;
            max-width: 90%;
            font-size: clamp(0.9rem, 2.5vw, 1.1rem);
            color: #2c5530;
            border: 2px solid #4CAF50;
        }

        .writing-area {
            width: 100%;
            max-width: 500px;
            height: 100px;
            padding: 15px;
            border: 3px solid #f7931e;
            border-radius: 15px;
            font-size: clamp(1rem, 3vw, 1.2rem);
            resize: none;
            margin: 20px auto;
            display: block;
            font-family: inherit;
        }

        .victory-screen {
            background: linear-gradient(45deg, #ffd700, #ffed4e, #4CAF50, #2196F3);
            background-size: 400% 400%;
            animation: celebration 3s infinite;
            position: relative;
            overflow: hidden;
        }

        .victory-screen::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><text y="50" font-size="20">🎉</text></svg>') repeat;
            opacity: 0.3;
            animation: float 4s ease-in-out infinite;
        }

        @keyframes celebration {
            0% { background-position: 0% 50%; }
            25% { background-position: 100% 50%; }
            50% { background-position: 100% 100%; }
            75% { background-position: 0% 100%; }
            100% { background-position: 0% 50%; }
        }

        .speak-btn {
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 20px;
            cursor: pointer;
            margin: 0 10px;
            font-size: clamp(0.8rem, 2vw, 1rem);
            border: 2px solid white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }

        .speak-btn:hover {
            background: linear-gradient(45deg, #F57C00, #E65100);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0,0,0,0.3);
        }

        .speak-btn:active {
            transform: translateY(0px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .level-title {
            font-size: clamp(1.5rem, 5vw, 2.5rem);
            color: #2c5530;
            margin: 20px 0;
            text-shadow: 2px 2px 0px #ffd23f;
        }

        .score {
            font-size: clamp(1.2rem, 4vw, 2rem);
            color: #2c5530;
            font-weight: bold;
            margin: 15px 0;
        }

        /* Enhanced Responsive design */
        @media (max-width: 768px) {
            .container { padding: 5px; }
            .story-text {
                margin: 10px auto;
                padding: 15px;
                font-size: clamp(0.9rem, 2.5vw, 1.2rem);
            }
            .word-options { gap: 10px; }
            .sentence-parts { gap: 8px; }
            .game-title { font-size: clamp(1.5rem, 6vw, 3rem); }
            .level-title { font-size: clamp(1.2rem, 4vw, 2rem); }
            .challenge-image {
                width: clamp(120px, 25vw, 180px);
                height: clamp(120px, 25vw, 180px);
            }
        }

        @media (max-width: 480px) {
            .keys-collected { gap: 5px; }
            .word-part {
                padding: 8px 12px;
                font-size: clamp(0.8rem, 2vw, 1rem);
            }
            .btn {
                margin: 8px 5px;
                padding: clamp(10px, 2.5vw, 15px) clamp(15px, 4vw, 25px);
                font-size: clamp(0.9rem, 2.5vw, 1.1rem);
            }
            .floating-element { display: none; } /* Hide floating elements on very small screens */
            .story-text { padding: 10px; }
            .sentence-builder { padding: 15px; }
            .reading-passage { padding: 15px; }
        }

        @media (max-width: 320px) {
            .game-title { font-size: clamp(1.2rem, 5vw, 2rem); }
            .btn {
                padding: 8px 12px;
                font-size: 0.9rem;
                margin: 5px 2px;
            }
            .word-options { gap: 8px; }
        }

        /* High contrast mode for accessibility */
        @media (prefers-contrast: high) {
            .btn { border-width: 4px; }
            .story-text { border-width: 4px; }
            .challenge-image { border-width: 6px; }
        }
    </style>
</head>
<body>
    <!-- Floating decorative elements -->
    <div class="floating-element floating-pineapple">🍍</div>
    <div class="floating-element floating-palm">🌴</div>
    <div class="floating-element floating-sun">☀️</div>

    <div class="container">
        <!-- Intro Screen -->
        <div class="screen active" id="intro">
            <h1 class="game-title">🍍 جزيرة الأناناس</h1>
            <div class="story-text">
                <p><strong>مرحباً بك في جزيرة الأناناس!</strong></p>
                <p>سكان الجزيرة فقدوا خريطة السعادة 🗺️</p>
                <p>لإعادتها، يجب أن تجمع 5 مفاتيح سحرية عبر إنهاء تحديات ممتعة!</p>
                <br>
                <p><em>Welcome to Pineapple Island!</em></p>
                <p><em>The island people lost the Map of Happiness</em></p>
                <p><em>To bring it back, you must collect 5 magic keys!</em></p>
            </div>
            <button class="btn" onclick="startGame()">🚀 ابدأ المغامرة - Start Adventure</button>
            <button class="speak-btn" onclick="speak('مرحباً بك في جزيرة الأناناس. سكان الجزيرة فقدوا خريطة السعادة', 'ar')">🔊 عربي</button>
            <button class="speak-btn" onclick="speak('Welcome to Pineapple Island. The island people lost the Map of Happiness', 'en')">🔊 English</button>
        </div>

        <!-- Vocabulary Challenge -->
        <div class="screen" id="vocabulary">
            <h2 class="level-title">🏪 سوق المفردات - Vocabulary Market</h2>
            <div class="keys-collected">
                <span class="key" id="key1">🗝️</span>
                <span class="key" id="key2">🗝️</span>
                <span class="key" id="key3">🗝️</span>
                <span class="key" id="key4">🗝️</span>
                <span class="key" id="key5">🗝️</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="vocab-progress"></div>
            </div>
            <div class="score" id="vocab-score">النقاط - Score: 0/5</div>
            
            <div id="vocab-challenge">
                <img class="challenge-image" id="vocab-image" src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='200' height='200'><rect width='200' height='200' fill='%23f7931e'/><text x='100' y='100' text-anchor='middle' dy='0.3em' font-size='80'>🍍</text></svg>" alt="Pineapple">
                <div class="word-options">
                    <button class="btn vocab-option" onclick="checkVocab(this, true)">Pineapple</button>
                    <button class="btn vocab-option" onclick="checkVocab(this, false)">Apple</button>
                    <button class="btn vocab-option" onclick="checkVocab(this, false)">Orange</button>
                </div>
                <div>
                    <button class="speak-btn" onclick="speak('أناناس', 'ar')">🔊 عربي</button>
                    <button class="speak-btn" onclick="speak('Pineapple', 'en')">🔊 English</button>
                </div>
            </div>
        </div>

        <!-- Grammar Challenge -->
        <div class="screen" id="grammar">
            <h2 class="level-title">🌳 غابة القواعد - Grammar Forest</h2>
            <div class="keys-collected">
                <span class="key collected" id="g-key1">🗝️</span>
                <span class="key" id="g-key2">🗝️</span>
                <span class="key" id="g-key3">🗝️</span>
                <span class="key" id="g-key4">🗝️</span>
                <span class="key" id="g-key5">🗝️</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="grammar-progress"></div>
            </div>
            <div class="score" id="grammar-score">النقاط - Score: 0/5</div>
            
            <div class="sentence-builder">
                <p id="grammar-question">أكمل الجملة - Complete the sentence:</p>
                <p><strong>"I _____ eat rice."</strong></p>
                <div class="word-options">
                    <button class="btn grammar-option" onclick="checkGrammar(this, true)">always</button>
                    <button class="btn grammar-option" onclick="checkGrammar(this, false)">some</button>
                    <button class="btn grammar-option" onclick="checkGrammar(this, false)">any</button>
                </div>
                <div>
                    <button class="speak-btn" onclick="speak('أنا دائماً آكل الأرز', 'ar')">🔊 عربي</button>
                    <button class="speak-btn" onclick="speak('I always eat rice', 'en')">🔊 English</button>
                </div>
            </div>
        </div>

        <!-- Recipe Challenge -->
        <div class="screen" id="recipe">
            <h2 class="level-title">🍲 نهر الوصفات - Recipe River</h2>
            <div class="keys-collected">
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key" id="r-key3">🗝️</span>
                <span class="key" id="r-key4">🗝️</span>
                <span class="key" id="r-key5">🗝️</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="recipe-progress"></div>
            </div>
            <div class="score" id="recipe-score">النقاط - Score: 0/4</div>
            
            <div class="sentence-builder">
                <p>رتب خطوات الوصفة - Arrange the recipe steps:</p>
                <div class="sentence-parts" id="recipe-parts">
                    <div class="word-part" draggable="true">Finally</div>
                    <div class="word-part" draggable="true">First</div>
                    <div class="word-part" draggable="true">Then</div>
                    <div class="word-part" draggable="true">Next</div>
                </div>
                <div class="drop-zone" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">1. _____ wash the rice</div>
                <div class="drop-zone" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">2. _____ cook it</div>
                <div class="drop-zone" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">3. _____ add salt</div>
                <div class="drop-zone" ondrop="drop(event)" ondragover="allowDrop(event)" ondragleave="dragLeave(event)">4. _____ eat with family</div>
                <button class="btn" onclick="checkRecipe()">✅ تحقق من الإجابة - Check Answer</button>
            </div>
        </div>

        <!-- Reading Challenge -->
        <div class="screen" id="reading">
            <h2 class="level-title">📖 كهف القراءة - Reading Cave</h2>
            <div class="keys-collected">
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key" id="read-key4">🗝️</span>
                <span class="key" id="read-key5">🗝️</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="reading-progress"></div>
            </div>
            <div class="score" id="reading-score">النقاط - Score: 0/3</div>
            
            <div class="reading-passage">
                <p><strong>الأطفال والطبيعة - Children and Nature</strong></p>
                <p>Children love to play in the garden. They water the plants and help their families grow vegetables. In the morning, they listen to the birds singing. The garden is a peaceful place where children learn about nature and discover new things every day.</p>
                <button class="speak-btn" onclick="speak('الأطفال يحبون اللعب في الحديقة. يسقون النباتات ويساعدون عائلاتهم في زراعة الخضروات', 'ar')">🔊 عربي</button>
                <button class="speak-btn" onclick="speak('Children love to play in the garden. They water the plants and help their families grow vegetables', 'en')">🔊 English</button>
            </div>
            
            <div id="reading-questions">
                <div class="question">
                    <p><strong>Where do children love to play?</strong></p>
                    <div class="word-options">
                        <button class="btn reading-option" onclick="checkReading(this, true)">In the garden</button>
                        <button class="btn reading-option" onclick="checkReading(this, false)">In the river</button>
                        <button class="btn reading-option" onclick="checkReading(this, false)">In the shop</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Final Challenge -->
        <div class="screen" id="final">
            <h2 class="level-title">🐊 التحدي الأخير - Final Challenge</h2>
            <div class="keys-collected">
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key" id="final-key5">🗝️</span>
            </div>
            
            <div class="sentence-builder">
                <p><strong>🐊 الحارس يريد منك كتابة جملة باستخدام ظرف تكرار + قيمة إيجابية</strong></p>
                <p><em>The Guardian wants you to write a sentence using frequency adverb + positive value</em></p>
                
                <p>أمثلة - Examples:</p>
                <p>"I always help my family" - "I never throw rubbish in the garden"</p>
                
                <textarea class="writing-area" id="final-sentence" placeholder="اكتب جملتك هنا... Write your sentence here..."></textarea>
                <button class="btn" onclick="checkFinal()">🗝️ احصل على المفتاح الأخير - Get Final Key</button>
                
                <div>
                    <button class="speak-btn" onclick="speak('أنا دائماً أساعد عائلتي', 'ar')">🔊 مثال عربي</button>
                    <button class="speak-btn" onclick="speak('I always help my family', 'en')">🔊 English Example</button>
                </div>
            </div>
        </div>

        <!-- Victory Screen -->
        <div class="screen victory-screen" id="victory">
            <h1 class="game-title">🎉 مبروك! Congratulations!</h1>
            <div class="keys-collected">
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
                <span class="key collected">🗝️</span>
            </div>
            <div class="story-text">
                <p><strong>🏆 لقد جمعت جميع المفاتيح الخمسة!</strong></p>
                <p>🗺️ أعدت خريطة السعادة إلى الجزيرة!</p>
                <p>🎊 أنت الآن مكتشف اللغة!</p>
                <br>
                <p><em>🎉 You collected all 5 keys and brought back the Map of Happiness!</em></p>
                <p><em>🌟 You are now a Language Explorer!</em></p>
            </div>
            <button class="btn" onclick="restartGame()">🔄 العب مرة أخرى - Play Again</button>
            <button class="speak-btn" onclick="speak('مبروك! لقد جمعت جميع المفاتيح وأصبحت مكتشف اللغة', 'ar')">🔊 عربي</button>
            <button class="speak-btn" onclick="speak('Congratulations! You collected all keys and became a Language Explorer', 'en')">🔊 English</button>
        </div>
    </div>

    <script>
        // Game state
        let currentScreen = 'intro';
        let vocabScore = 0;
        let grammarScore = 0;
        let recipeScore = 0;
        let readingScore = 0;
        let currentVocabIndex = 0;
        let currentGrammarIndex = 0;
        let currentReadingIndex = 0;
        let gameStartTime = null;
        let totalScore = 0;

        // Save progress to localStorage
        function saveProgress() {
            const progress = {
                vocabScore,
                grammarScore,
                recipeScore,
                readingScore,
                currentVocabIndex,
                currentGrammarIndex,
                currentReadingIndex,
                currentScreen,
                totalScore
            };
            localStorage.setItem('pineappleIslandProgress', JSON.stringify(progress));
        }

        // Load progress from localStorage
        function loadProgress() {
            const saved = localStorage.getItem('pineappleIslandProgress');
            if (saved) {
                const progress = JSON.parse(saved);
                vocabScore = progress.vocabScore || 0;
                grammarScore = progress.grammarScore || 0;
                recipeScore = progress.recipeScore || 0;
                readingScore = progress.readingScore || 0;
                currentVocabIndex = progress.currentVocabIndex || 0;
                currentGrammarIndex = progress.currentGrammarIndex || 0;
                currentReadingIndex = progress.currentReadingIndex || 0;
                totalScore = progress.totalScore || 0;
            }
        }
        
        // Vocabulary data
        const vocabData = [
            {
                image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='200' height='200'><rect width='200' height='200' fill='%23f7931e'/><text x='100' y='100' text-anchor='middle' dy='0.3em' font-size='80'>🍍</text></svg>",
                correct: "Pineapple",
                options: ["Pineapple", "Apple", "Orange"],
                arabic: "أناناس"
            },
            {
                image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='200' height='200'><rect width='200' height='200' fill='%234CAF50'/><text x='100' y='100' text-anchor='middle' dy='0.3em' font-size='80'>🐊</text></svg>",
                correct: "Crocodile",
                options: ["Crocodile", "Snake", "Lizard"],
                arabic: "تمساح"
            },
            {
                image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='200' height='200'><rect width='200' height='200' fill='%232196F3'/><text x='100' y='100' text-anchor='middle' dy='0.3em' font-size='80'>🌴</text></svg>",
                correct: "Palm Tree",
                options: ["Palm Tree", "Oak Tree", "Pine Tree"],
                arabic: "نخلة"
            },
            {
                image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='200' height='200'><rect width='200' height='200' fill='%23FF9800'/><text x='100' y='100' text-anchor='middle' dy='0.3em' font-size='80'>🏝️</text></svg>",
                correct: "Island",
                options: ["Island", "Mountain", "Desert"],
                arabic: "جزيرة"
            },
            {
                image: "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' width='200' height='200'><rect width='200' height='200' fill='%23E91E63'/><text x='100' y='100' text-anchor='middle' dy='0.3em' font-size='80'>🗝️</text></svg>",
                correct: "Key",
                options: ["Key", "Lock", "Door"],
                arabic: "مفتاح"
            }
        ];

        // Grammar data
        const grammarData = [
            {
                sentence: "I _____ eat rice.",
                correct: "always",
                options: ["always", "some", "any"],
                arabic: "أنا دائماً آكل الأرز"
            },
            {
                sentence: "There is _____ crocodile.",
                correct: "a",
                options: ["a", "an", "any"],
                arabic: "هناك تمساح"
            },
            {
                sentence: "Children _____ play in the garden.",
                correct: "often",
                options: ["often", "much", "very"],
                arabic: "الأطفال غالباً يلعبون في الحديقة"
            },
            {
                sentence: "We _____ throw rubbish.",
                correct: "never",
                options: ["never", "always", "some"],
                arabic: "نحن لا نرمي القمامة أبداً"
            },
            {
                sentence: "I _____ help my family.",
                correct: "sometimes",
                options: ["sometimes", "much", "very"],
                arabic: "أنا أحياناً أساعد عائلتي"
            }
        ];

        // Reading questions
        const readingQuestions = [
            {
                question: "Where do children love to play?",
                correct: "In the garden",
                options: ["In the garden", "In the river", "In the shop"]
            },
            {
                question: "What do children do in the garden?",
                correct: "Water plants",
                options: ["Water plants", "Watch TV", "Sleep"]
            },
            {
                question: "When do birds sing?",
                correct: "In the morning",
                options: ["In the morning", "At night", "Never"]
            }
        ];

        // Enhanced Speech synthesis function
        function speak(text, lang) {
            if ('speechSynthesis' in window) {
                // Stop any current speech
                speechSynthesis.cancel();

                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = lang === 'ar' ? 'ar-SA' : 'en-US';
                utterance.rate = 0.6; // Slower speech for children
                utterance.pitch = 1.1; // Slightly higher pitch for children
                utterance.volume = 0.9;

                // Add visual feedback
                const speakButtons = document.querySelectorAll('.speak-btn');
                speakButtons.forEach(btn => btn.style.opacity = '0.7');

                utterance.onend = function() {
                    speakButtons.forEach(btn => btn.style.opacity = '1');
                };

                utterance.onerror = function() {
                    speakButtons.forEach(btn => btn.style.opacity = '1');
                    console.log('Speech synthesis error');
                };

                // Ensure voices are loaded before speaking
                if (speechSynthesis.getVoices().length === 0) {
                    speechSynthesis.addEventListener('voiceschanged', function() {
                        speechSynthesis.speak(utterance);
                    }, { once: true });
                } else {
                    setTimeout(() => {
                        speechSynthesis.speak(utterance);
                    }, 100);
                }
            } else {
                alert('عذراً، متصفحك لا يدعم النطق - Sorry, your browser does not support speech');
            }
        }

        // Screen management
        function showScreen(screenId) {
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });
            document.getElementById(screenId).classList.add('active');
            currentScreen = screenId;
        }

        function startGame() {
            gameStartTime = new Date();
            showScreen('vocabulary');
            loadVocabChallenge();
            saveProgress();
        }

        function restartGame() {
            // Reset all scores
            vocabScore = 0;
            grammarScore = 0;
            recipeScore = 0;
            readingScore = 0;
            currentVocabIndex = 0;
            currentGrammarIndex = 0;
            currentReadingIndex = 0;
            totalScore = 0;
            gameStartTime = null;

            // Reset all keys
            document.querySelectorAll('.key').forEach(key => {
                key.classList.remove('collected');
            });

            // Reset progress bars
            document.querySelectorAll('.progress-fill').forEach(bar => {
                bar.style.width = '0%';
            });

            // Reset recipe challenge
            const dropZones = document.querySelectorAll('.drop-zone');
            dropZones.forEach((zone, index) => {
                zone.style.backgroundColor = '';
                zone.style.color = '';
                zone.style.border = '3px dashed #f7931e';
                zone.classList.remove('filled');
                const stepNumber = index + 1;
                const stepText = ['wash the rice', 'cook it', 'add salt', 'eat with family'][index];
                zone.textContent = `${stepNumber}. _____ ${stepText}`;
            });

            // Show all word parts again
            document.querySelectorAll('.word-part').forEach(part => {
                part.style.display = 'block';
                part.style.opacity = '1';
                part.style.transform = 'scale(1)';
            });

            // Clear localStorage
            localStorage.removeItem('pineappleIslandProgress');

            showScreen('intro');
            speak('مرحباً بك مرة أخرى في جزيرة الأناناس! Welcome back to Pineapple Island!', 'ar');
        }

        // Vocabulary Challenge
        function loadVocabChallenge() {
            if (currentVocabIndex >= vocabData.length) {
                document.getElementById('key1').classList.add('collected');
                setTimeout(() => showScreen('grammar'), 1000);
                loadGrammarChallenge();
                return;
            }

            const data = vocabData[currentVocabIndex];
            document.getElementById('vocab-image').src = data.image;
            
            const buttons = document.querySelectorAll('.vocab-option');
            const shuffledOptions = [...data.options].sort(() => Math.random() - 0.5);
            
            buttons.forEach((btn, index) => {
                btn.textContent = shuffledOptions[index];
                btn.classList.remove('wrong', 'correct');
                btn.disabled = false;
                btn.onclick = () => checkVocab(btn, shuffledOptions[index] === data.correct);
            });

            updateVocabProgress();
        }

        function checkVocab(button, isCorrect) {
            const buttons = document.querySelectorAll('.vocab-option');
            buttons.forEach(btn => btn.disabled = true);

            if (isCorrect) {
                button.classList.add('correct');
                vocabScore++;
                totalScore += 10;
                speak('صحيح! Correct!', 'ar');
                saveProgress();
                setTimeout(() => {
                    currentVocabIndex++;
                    loadVocabChallenge();
                }, 1500);
            } else {
                button.classList.add('wrong');
                speak('حاول مرة أخرى Try again', 'ar');
                setTimeout(() => {
                    buttons.forEach(btn => {
                        btn.disabled = false;
                        btn.classList.remove('wrong', 'correct');
                    });
                }, 1000);
            }
        }

        function updateVocabProgress() {
            const progress = (vocabScore / 5) * 100;
            document.getElementById('vocab-progress').style.width = progress + '%';
            document.getElementById('vocab-score').textContent = `النقاط - Score: ${vocabScore}/5`;
        }

        // Grammar Challenge
        function loadGrammarChallenge() {
            if (currentGrammarIndex >= grammarData.length) {
                document.getElementById('g-key2').classList.add('collected');
                setTimeout(() => showScreen('recipe'), 1000);
                return;
            }

            const data = grammarData[currentGrammarIndex];
            document.querySelector('#grammar-question').nextElementSibling.innerHTML = `<strong>"${data.sentence}"</strong>`;
            
            const buttons = document.querySelectorAll('.grammar-option');
            const shuffledOptions = [...data.options].sort(() => Math.random() - 0.5);
            
            buttons.forEach((btn, index) => {
                btn.textContent = shuffledOptions[index];
                btn.classList.remove('wrong', 'correct');
                btn.disabled = false;
                btn.onclick = () => checkGrammar(btn, shuffledOptions[index] === data.correct);
            });

            updateGrammarProgress();
        }

        function checkGrammar(button, isCorrect) {
            const buttons = document.querySelectorAll('.grammar-option');
            buttons.forEach(btn => btn.disabled = true);

            if (isCorrect) {
                button.classList.add('correct');
                grammarScore++;
                speak('ممتاز! Excellent!', 'ar');
                setTimeout(() => {
                    currentGrammarIndex++;
                    loadGrammarChallenge();
                }, 1500);
            } else {
                button.classList.add('wrong');
                speak('حاول مرة أخرى Try again', 'ar');
                setTimeout(() => {
                    buttons.forEach(btn => {
                        btn.disabled = false;
                        btn.classList.remove('wrong', 'correct');
                    });
                }, 1000);
            }
        }

        function updateGrammarProgress() {
            const progress = (grammarScore / 5) * 100;
            document.getElementById('grammar-progress').style.width = progress + '%';
            document.getElementById('grammar-score').textContent = `النقاط - Score: ${grammarScore}/5`;
        }

        // Enhanced Recipe Challenge (Drag and Drop)
        function allowDrop(ev) {
            ev.preventDefault();
            ev.target.style.backgroundColor = 'rgba(76, 175, 80, 0.3)';
        }

        function drag(ev) {
            ev.dataTransfer.setData("text", ev.target.textContent);
            ev.target.style.opacity = '0.5';
        }

        function drop(ev) {
            ev.preventDefault();
            const data = ev.dataTransfer.getData("text");
            ev.target.style.backgroundColor = '';

            if (!ev.target.classList.contains('filled') && ev.target.classList.contains('drop-zone')) {
                ev.target.textContent = ev.target.textContent.replace('_____', data);
                ev.target.classList.add('filled');

                // Find and hide the dragged element
                const wordParts = document.querySelectorAll('.word-part');
                wordParts.forEach(part => {
                    if (part.textContent === data && part.style.display !== 'none') {
                        part.style.display = 'none';
                        return;
                    }
                });
            }
        }

        function dragLeave(ev) {
            ev.target.style.backgroundColor = '';
        }

        // Add drag events to word parts
        document.addEventListener('DOMContentLoaded', function() {
            const wordParts = document.querySelectorAll('.word-part');
            wordParts.forEach(part => {
                part.addEventListener('dragstart', drag);
            });
        });

        function checkRecipe() {
            const dropZones = document.querySelectorAll('.drop-zone');
            const expectedOrder = ['First', 'Then', 'Next', 'Finally'];
            let correctCount = 0;

            dropZones.forEach((zone, index) => {
                if (zone.textContent.includes(expectedOrder[index])) {
                    correctCount++;
                    zone.style.backgroundColor = '#4CAF50';
                    zone.style.color = 'white';
                    zone.style.border = '3px solid #2e7d32';
                } else {
                    zone.style.backgroundColor = '#f44336';
                    zone.style.color = 'white';
                    zone.style.border = '3px solid #d32f2f';
                }
            });

            if (correctCount === 4) {
                recipeScore = 4;
                document.getElementById('r-key3').classList.add('collected');
                speak('رائع! الترتيب صحيح Perfect! Correct order!', 'ar');

                // Add celebration effect
                dropZones.forEach(zone => {
                    zone.style.animation = 'pulse 0.5s';
                });

                setTimeout(() => {
                    showScreen('reading');
                    loadReadingChallenge();
                }, 2000);
            } else {
                speak('حاول مرة أخرى Try again', 'ar');
                setTimeout(() => {
                    // Reset the recipe challenge
                    dropZones.forEach((zone, index) => {
                        zone.style.backgroundColor = '';
                        zone.style.color = '';
                        zone.style.border = '3px dashed #f7931e';
                        zone.classList.remove('filled');
                        const stepNumber = index + 1;
                        const stepText = ['wash the rice', 'cook it', 'add salt', 'eat with family'][index];
                        zone.textContent = `${stepNumber}. _____ ${stepText}`;
                    });

                    // Show all word parts again
                    document.querySelectorAll('.word-part').forEach(part => {
                        part.style.display = 'block';
                        part.style.opacity = '1';
                    });
                }, 1500);
            }

            updateRecipeProgress();
        }

        function updateRecipeProgress() {
            const progress = (recipeScore / 4) * 100;
            document.getElementById('recipe-progress').style.width = progress + '%';
            document.getElementById('recipe-score').textContent = `النقاط - Score: ${recipeScore}/4`;
        }

        // Reading Challenge
        function loadReadingChallenge() {
            if (currentReadingIndex >= readingQuestions.length) {
                document.getElementById('read-key4').classList.add('collected');
                setTimeout(() => showScreen('final'), 1000);
                return;
            }

            const question = readingQuestions[currentReadingIndex];
            const questionDiv = document.querySelector('.question');
            questionDiv.querySelector('p').innerHTML = `<strong>${question.question}</strong>`;
            
            const buttons = questionDiv.querySelectorAll('.reading-option');
            const shuffledOptions = [...question.options].sort(() => Math.random() - 0.5);
            
            buttons.forEach((btn, index) => {
                btn.textContent = shuffledOptions[index];
                btn.classList.remove('wrong', 'correct');
                btn.disabled = false;
                btn.onclick = () => checkReading(btn, shuffledOptions[index] === question.correct);
            });

            updateReadingProgress();
        }

        function checkReading(button, isCorrect) {
            const buttons = document.querySelectorAll('.reading-option');
            buttons.forEach(btn => btn.disabled = true);

            if (isCorrect) {
                button.classList.add('correct');
                readingScore++;
                speak('ممتاز في القراءة! Excellent reading!', 'ar');
                setTimeout(() => {
                    currentReadingIndex++;
                    loadReadingChallenge();
                }, 1500);
            } else {
                button.classList.add('wrong');
                speak('اقرأ النص مرة أخرى Read the text again', 'ar');
                setTimeout(() => {
                    buttons.forEach(btn => {
                        btn.disabled = false;
                        btn.classList.remove('wrong', 'correct');
                    });
                }, 1000);
            }
        }

        function updateReadingProgress() {
            const progress = (readingScore / 3) * 100;
            document.getElementById('reading-progress').style.width = progress + '%';
            document.getElementById('reading-score').textContent = `النقاط - Score: ${readingScore}/3`;
        }

        // Final Challenge
        function checkFinal() {
            const sentence = document.getElementById('final-sentence').value.toLowerCase().trim();
            const frequencyWords = ['always', 'never', 'often', 'sometimes', 'usually'];
            const positiveWords = ['help', 'clean', 'protect', 'care', 'love', 'respect', 'plant', 'save', 'share'];
            
            const hasFrequency = frequencyWords.some(word => sentence.includes(word));
            const hasPositive = positiveWords.some(word => sentence.includes(word));
            
            if (hasFrequency && hasPositive && sentence.length > 10) {
                document.getElementById('final-key5').classList.add('collected');
                speak('مبروك! حصلت على المفتاح الأخير Congratulations! You got the final key!', 'ar');
                setTimeout(() => showScreen('victory'), 2000);
            } else {
                speak('تحتاج لاستخدام ظرف تكرار وقيمة إيجابية You need frequency adverb and positive value', 'ar');
                document.getElementById('final-sentence').style.borderColor = '#f44336';
                setTimeout(() => {
                    document.getElementById('final-sentence').style.borderColor = '#f7931e';
                }, 2000);
            }
        }

        // Initialize game when page loads
        window.onload = function() {
            // Load saved progress
            loadProgress();

            // Initialize drag and drop
            const wordParts = document.querySelectorAll('.word-part');
            wordParts.forEach(part => {
                part.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', e.target.textContent);
                    e.dataTransfer.effectAllowed = 'move';
                });
            });

            // Add keyboard support
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && currentScreen === 'intro') {
                    startGame();
                }
                if (e.key === 'Escape') {
                    if (confirm('هل تريد العودة للبداية؟ Do you want to restart?')) {
                        restartGame();
                    }
                }
            });

            // Add visual feedback for loading
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s';
                document.body.style.opacity = '1';
            }, 100);
        };

        // Enhanced Touch support for mobile devices
        let draggedElement = null;
        let touchStartPos = { x: 0, y: 0 };

        document.addEventListener('touchstart', function(e) {
            if (e.target.classList.contains('word-part') && e.target.style.display !== 'none') {
                draggedElement = e.target;
                e.target.style.opacity = '0.5';
                e.target.style.transform = 'scale(1.1)';
                e.target.style.zIndex = '1000';

                touchStartPos.x = e.touches[0].clientX;
                touchStartPos.y = e.touches[0].clientY;

                // Add visual feedback
                e.target.style.boxShadow = '0 8px 25px rgba(0,0,0,0.4)';
            }
        });

        document.addEventListener('touchmove', function(e) {
            if (draggedElement) {
                e.preventDefault();

                // Move the element with finger
                const touch = e.touches[0];
                const deltaX = touch.clientX - touchStartPos.x;
                const deltaY = touch.clientY - touchStartPos.y;

                draggedElement.style.transform = `translate(${deltaX}px, ${deltaY}px) scale(1.1)`;

                // Highlight drop zones
                const dropTarget = document.elementFromPoint(touch.clientX, touch.clientY);
                document.querySelectorAll('.drop-zone').forEach(zone => {
                    zone.style.backgroundColor = '';
                });

                if (dropTarget && dropTarget.classList.contains('drop-zone') && !dropTarget.classList.contains('filled')) {
                    dropTarget.style.backgroundColor = 'rgba(76, 175, 80, 0.3)';
                }
            }
        });

        document.addEventListener('touchend', function(e) {
            if (draggedElement) {
                const dropTarget = document.elementFromPoint(e.changedTouches[0].clientX, e.changedTouches[0].clientY);

                if (dropTarget && dropTarget.classList.contains('drop-zone') && !dropTarget.classList.contains('filled')) {
                    dropTarget.textContent = dropTarget.textContent.replace('_____', draggedElement.textContent);
                    dropTarget.classList.add('filled');
                    draggedElement.style.display = 'none';

                    // Success feedback
                    dropTarget.style.backgroundColor = '#4CAF50';
                    dropTarget.style.color = 'white';
                    setTimeout(() => {
                        dropTarget.style.backgroundColor = '';
                        dropTarget.style.color = '';
                    }, 1000);
                } else {
                    // Return to original position
                    draggedElement.style.transform = 'scale(1)';
                }

                // Reset styles
                draggedElement.style.opacity = '1';
                draggedElement.style.zIndex = '';
                draggedElement.style.boxShadow = '';

                // Clear drop zone highlights
                document.querySelectorAll('.drop-zone').forEach(zone => {
                    zone.style.backgroundColor = '';
                });

                draggedElement = null;
            }
        });