<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار لعبة جزيرة الأناناس</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd23f);
            min-height: 100vh;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        .test-item {
            margin: 15px 0;
            padding: 10px;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-item.fail {
            border-color: #f44336;
            background: #ffebee;
        }
        .test-item.pass {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🍍 اختبار لعبة جزيرة الأناناس</h1>
        
        <div class="test-item" id="responsive-test">
            <h3>📱 اختبار التجاوب مع الشاشات</h3>
            <p>اختبار عرض اللعبة على أحجام شاشات مختلفة</p>
            <button onclick="testResponsive('desktop')">شاشة كبيرة</button>
            <button onclick="testResponsive('tablet')">تابلت</button>
            <button onclick="testResponsive('mobile')">موبايل</button>
            <div id="responsive-result"></div>
        </div>

        <div class="test-item" id="speech-test">
            <h3>🔊 اختبار النطق</h3>
            <p>اختبار وظيفة النطق باللغتين العربية والإنجليزية</p>
            <button onclick="testSpeech('ar')">اختبار العربية</button>
            <button onclick="testSpeech('en')">اختبار الإنجليزية</button>
            <div id="speech-result"></div>
        </div>

        <div class="test-item" id="interaction-test">
            <h3>🎮 اختبار التفاعل</h3>
            <p>اختبار الأزرار والتفاعلات</p>
            <button onclick="testInteraction()">اختبار التفاعل</button>
            <div id="interaction-result"></div>
        </div>

        <div class="test-item" id="drag-test">
            <h3>🖱️ اختبار السحب والإفلات</h3>
            <p>اختبار وظيفة السحب والإفلات للأجهزة المختلفة</p>
            <button onclick="testDragDrop()">اختبار السحب والإفلات</button>
            <div id="drag-result"></div>
        </div>

        <h2>معاينة اللعبة:</h2>
        <iframe src="Pineapple Island.html" id="game-frame"></iframe>

        <div class="test-item">
            <h3>📋 تقرير الاختبار النهائي</h3>
            <div id="final-report">
                <p>انقر على "تشغيل جميع الاختبارات" للحصول على تقرير شامل</p>
            </div>
            <button onclick="runAllTests()" style="background: #2196F3;">تشغيل جميع الاختبارات</button>
        </div>
    </div>

    <script>
        let testResults = {};

        function testResponsive(size) {
            const frame = document.getElementById('game-frame');
            const result = document.getElementById('responsive-result');
            
            switch(size) {
                case 'desktop':
                    frame.style.width = '100%';
                    frame.style.height = '600px';
                    break;
                case 'tablet':
                    frame.style.width = '768px';
                    frame.style.height = '500px';
                    break;
                case 'mobile':
                    frame.style.width = '375px';
                    frame.style.height = '400px';
                    break;
            }
            
            result.innerHTML = `<p style="color: green;">✅ تم تغيير حجم الشاشة إلى ${size}</p>`;
            testResults.responsive = true;
            updateTestStatus('responsive-test', true);
        }

        function testSpeech(lang) {
            const result = document.getElementById('speech-result');
            
            if ('speechSynthesis' in window) {
                const text = lang === 'ar' ? 'مرحباً بك في جزيرة الأناناس' : 'Welcome to Pineapple Island';
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = lang === 'ar' ? 'ar-SA' : 'en-US';
                utterance.rate = 0.6;
                
                speechSynthesis.speak(utterance);
                result.innerHTML = `<p style="color: green;">✅ النطق يعمل باللغة ${lang === 'ar' ? 'العربية' : 'الإنجليزية'}</p>`;
                testResults.speech = true;
                updateTestStatus('speech-test', true);
            } else {
                result.innerHTML = `<p style="color: red;">❌ المتصفح لا يدعم النطق</p>`;
                testResults.speech = false;
                updateTestStatus('speech-test', false);
            }
        }

        function testInteraction() {
            const result = document.getElementById('interaction-result');
            
            // Test if buttons are clickable
            const testButton = document.createElement('button');
            testButton.textContent = 'زر اختبار';
            testButton.onclick = function() {
                result.innerHTML = `<p style="color: green;">✅ الأزرار تعمل بشكل صحيح</p>`;
                testResults.interaction = true;
                updateTestStatus('interaction-test', true);
                testButton.remove();
            };
            
            result.appendChild(testButton);
            result.innerHTML += `<p>انقر على زر الاختبار أعلاه</p>`;
        }

        function testDragDrop() {
            const result = document.getElementById('drag-result');
            
            // Create a simple drag and drop test
            const dragElement = document.createElement('div');
            dragElement.textContent = 'اسحبني';
            dragElement.draggable = true;
            dragElement.style.cssText = 'background: #4CAF50; color: white; padding: 10px; border-radius: 5px; cursor: move; display: inline-block; margin: 10px;';
            
            const dropZone = document.createElement('div');
            dropZone.textContent = 'أفلتني هنا';
            dropZone.style.cssText = 'border: 2px dashed #ccc; padding: 20px; margin: 10px; border-radius: 5px;';
            
            dragElement.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', 'test');
            });
            
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.style.backgroundColor = '#e8f5e8';
            });
            
            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.textContent = 'تم الإفلات بنجاح!';
                dropZone.style.backgroundColor = '#4CAF50';
                dropZone.style.color = 'white';
                testResults.dragdrop = true;
                updateTestStatus('drag-test', true);
            });
            
            result.appendChild(dragElement);
            result.appendChild(dropZone);
        }

        function updateTestStatus(testId, passed) {
            const element = document.getElementById(testId);
            element.className = 'test-item ' + (passed ? 'pass' : 'fail');
        }

        function runAllTests() {
            testResponsive('desktop');
            setTimeout(() => testSpeech('ar'), 1000);
            setTimeout(() => testSpeech('en'), 2000);
            setTimeout(() => testInteraction(), 3000);
            setTimeout(() => testDragDrop(), 4000);
            
            setTimeout(() => {
                const report = document.getElementById('final-report');
                const passedTests = Object.values(testResults).filter(result => result).length;
                const totalTests = Object.keys(testResults).length;
                
                report.innerHTML = `
                    <h4>نتائج الاختبار:</h4>
                    <p>✅ اختبارات نجحت: ${passedTests}</p>
                    <p>📊 إجمالي الاختبارات: ${totalTests}</p>
                    <p style="color: ${passedTests === totalTests ? 'green' : 'orange'};">
                        ${passedTests === totalTests ? '🎉 جميع الاختبارات نجحت!' : '⚠️ بعض الاختبارات تحتاج مراجعة'}
                    </p>
                `;
            }, 5000);
        }
    </script>
</body>
</html>
